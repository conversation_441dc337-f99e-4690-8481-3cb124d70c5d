<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Dental Smyle - Case Study | Jaykee Aba-a</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://fonts.bunny.net/css?family=inter:300,400,500,600,700|space-grotesk:500" rel="stylesheet">
  <script src="https://unpkg.com/lucide@latest"></script>
  <style>
    body{font-family:'Inter',sans-serif;background:#0e0e10;color:#e5e7eb;}
    .animate{opacity:0;transform:translateY(40px);transition:all .8s cubic-bezier(.22,1,.36,1);}
    .animate.show{opacity:1;transform:none;}
    .divider{border-top:1px solid rgba(255,255,255,.08);}
    .outline{outline:1px solid rgba(255,255,255,.08);}
    ::selection{background:#6366f1;color:#fff;}
    a{transition:.3s;}
    a:hover{color:#a5b4fc;}
    .gradient-text{background:linear-gradient(90deg,#6366f1,#8b5cf6 60%,#ec4899);-webkit-background-clip:text;color:transparent;}
    input,textarea{background:#1a1a1d;border:1px solid rgba(255,255,255,.1);}
    input:focus,textarea:focus{outline:none;border-color:#6366f1;}
  </style>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="min-h-screen flex flex-col">
  <!-- NAV -->
  <header class="fixed top-0 inset-x-0 z-50 backdrop-blur-sm bg-black/30">
    <div class="mx-auto max-w-7xl flex items-center justify-between px-6 py-4">
      <h1 class="text-lg tracking-tight font-semibold gradient-text">JK</h1>
      <nav class="hidden sm:flex items-center gap-6 text-sm">
        <a href="v2.html#work">Work</a><a href="v2.html#services">Services</a><a href="v2.html#about">About</a><a href="v2.html#contact">Contact</a>
      </nav>
      <div class="sm:flex hidden items-center gap-4">
        <a href="v2.html#contact" class="text-xs font-medium px-4 py-2 outline rounded-md hover:bg-white/5">Let's talk</a>
      </div>
      <button id="menuBtn" class="sm:hidden">
        <i data-lucide="menu" class="stroke-[1.5] w-6 h-6"></i>
      </button>
    </div>
    <!-- mobile -->
    <div id="mobileNav" class="sm:hidden px-6 pt-2 pb-4 space-y-2 hidden">
      <a href="v2.html#work" class="block">Work</a>
      <a href="v2.html#services" class="block">Services</a>
      <a href="v2.html#about" class="block">About</a>
      <a href="v2.html#contact" class="block">Contact</a>
    </div>
  </header>

  <!-- HERO -->
  <section class="relative flex flex-col items-center justify-center text-center px-6 pt-40 pb-16">
    <div class="flex items-center gap-2 mb-6 animate">
      <a href="v2.html" class="flex items-center gap-1 text-sm opacity-70 hover:opacity-100">
        <i data-lucide="arrow-left" class="stroke-[1.5] w-4 h-4"></i>
        Back to portfolio
      </a>
    </div>
    <h1 class="text-4xl sm:text-5xl md:text-6xl font-semibold tracking-tight animate delay-[100ms]">Dental Smyle</h1>
    <p class="mt-6 max-w-2xl text-sm sm:text-base opacity-80 animate delay-[250ms]">Professional dental practice website featuring modern design and comprehensive dental services for Bright Smile Dental.</p>
    <div class="mt-10 flex gap-4 animate delay-[400ms]">
      <a href="https://dental-smyle.vercel.app/" target="_blank" class="px-6 py-3 bg-white text-black rounded-md text-sm font-medium hover:bg-white/90">Visit website</a>
      <a href="#details" class="px-6 py-3 outline rounded-md text-sm font-medium hover:bg-white/5">View details</a>
    </div>
  </section>

  <!-- PROJECT IMAGE -->
  <section class="mx-auto max-w-7xl px-6 pb-16">
    <div class="animate delay-[200ms]">
      <img src="dental.png" alt="Dental Smyle Website" class="w-full rounded-lg outline">
    </div>
  </section>

  <div class="divider mx-6"></div>

  <!-- PROJECT OVERVIEW -->
  <section id="details" class="mx-auto max-w-7xl px-6 py-24">
    <div class="grid lg:grid-cols-3 gap-12">
      <div class="lg:col-span-2">
        <h2 class="text-3xl font-semibold tracking-tight animate">Project Overview</h2>
        <p class="mt-6 text-sm opacity-80 leading-relaxed animate delay-[100ms]">
          I developed a comprehensive dental practice website for Bright Smile Dental, focusing on creating a professional, trustworthy online presence that showcases their expertise and services. The website features a clean, modern design with intuitive navigation and comprehensive service information.
        </p>
        <p class="mt-4 text-sm opacity-80 leading-relaxed animate delay-[150ms]">
          <strong>Design Vision:</strong> Professional, clean interface emphasizing trust and expertise. Modern layout showcasing dental services with patient testimonials and easy appointment booking. Mobile-first responsive design ensuring accessibility across all devices.
        </p>

        <h3 class="text-2xl font-semibold tracking-tight mt-12 animate delay-[200ms]">Key Features</h3>
        <ul class="mt-6 space-y-3 text-sm">
          <li class="flex gap-2 items-start animate delay-[250ms]">
            <i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400 mt-0.5 flex-shrink-0"></i>
            <span><strong>Comprehensive service catalog</strong> with detailed descriptions of general, cosmetic, and specialized dental services</span>
          </li>
          <li class="flex gap-2 items-start animate delay-[300ms]">
            <i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400 mt-0.5 flex-shrink-0"></i>
            <span><strong>Professional team showcase</strong> highlighting 15+ years of experience and expertise</span>
          </li>
          <li class="flex gap-2 items-start animate delay-[350ms]">
            <i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400 mt-0.5 flex-shrink-0"></i>
            <span><strong>Patient testimonials</strong> and reviews section building trust and credibility</span>
          </li>
          <li class="flex gap-2 items-start animate delay-[400ms]">
            <i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400 mt-0.5 flex-shrink-0"></i>
            <span><strong>Online appointment booking</strong> system with contact form integration</span>
          </li>
          <li class="flex gap-2 items-start animate delay-[450ms]">
            <i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400 mt-0.5 flex-shrink-0"></i>
            <span><strong>Emergency care information</strong> and 24/7 contact details</span>
          </li>
        </ul>

        <h3 class="text-2xl font-semibold tracking-tight mt-12 animate delay-[500ms]">Website Structure & Services</h3>
        <p class="mt-4 text-sm opacity-80 leading-relaxed animate delay-[550ms]">
          The website features a comprehensive structure with dedicated sections for all dental services:
        </p>
        <div class="mt-6 grid md:grid-cols-2 gap-6">
          <div class="p-4 outline rounded-lg animate delay-[600ms]">
            <h4 class="font-medium text-indigo-400 mb-2">General Dentistry</h4>
            <p class="text-xs opacity-70">Routine cleanings, fillings, and preventive care to maintain oral health.</p>
          </div>
          <div class="p-4 outline rounded-lg animate delay-[650ms]">
            <h4 class="font-medium text-indigo-400 mb-2">Cosmetic Dentistry</h4>
            <p class="text-xs opacity-70">Teeth whitening, veneers, and smile makeovers for confident, beautiful smiles.</p>
          </div>
          <div class="p-4 outline rounded-lg animate delay-[700ms]">
            <h4 class="font-medium text-indigo-400 mb-2">Orthodontics</h4>
            <p class="text-xs opacity-70">Braces and Invisalign treatments to straighten teeth and improve bite alignment.</p>
          </div>
          <div class="p-4 outline rounded-lg animate delay-[750ms]">
            <h4 class="font-medium text-indigo-400 mb-2">Oral Surgery</h4>
            <p class="text-xs opacity-70">Tooth extractions, implants, and surgical procedures with expert care.</p>
          </div>
          <div class="p-4 outline rounded-lg animate delay-[800ms]">
            <h4 class="font-medium text-indigo-400 mb-2">Pediatric Dentistry</h4>
            <p class="text-xs opacity-70">Specialized dental care for children in a comfortable, friendly environment.</p>
          </div>
          <div class="p-4 outline rounded-lg animate delay-[850ms]">
            <h4 class="font-medium text-indigo-400 mb-2">Emergency Care</h4>
            <p class="text-xs opacity-70">24/7 emergency dental services for urgent dental problems and pain relief.</p>
          </div>
        </div>

        <h3 class="text-2xl font-semibold tracking-tight mt-12 animate delay-[900ms]">Patient Experience</h3>
        <p class="mt-4 text-sm opacity-80 leading-relaxed animate delay-[950ms]">
          The website showcases impressive statistics: <strong>15+ years of experience</strong>, <strong>5000+ happy patients</strong>, and a <strong>98% success rate</strong>. Patient testimonials from verified patients like Sarah Johnson, Michael Chen, and Emily Rodriguez highlight the exceptional care and professional service.
        </p>

        <h3 class="text-2xl font-semibold tracking-tight mt-12 animate delay-[1000ms]">Color Palette & Style</h3>
        <p class="mt-4 text-sm opacity-80 leading-relaxed animate delay-[1050ms]">
          <strong>Primary:</strong> Professional medical blue and clean whites
        </p>
        <p class="mt-2 text-sm opacity-80 leading-relaxed animate delay-[1100ms]">
          <strong>Accents:</strong> Soft blues and medical greens <strong>Typography:</strong> Clean, readable fonts <strong>Animation:</strong> Smooth, professional transitions
        </p>
      </div>

      <!-- PROJECT DETAILS SIDEBAR -->
      <div class="space-y-8">
        <div class="animate delay-[100ms]">
          <h4 class="font-semibold mb-4">Project Details</h4>
          <div class="space-y-3 text-sm">
            <div>
              <span class="opacity-70">Client:</span><br>
              <span class="font-medium">Bright Smile Dental</span>
            </div>
            <div>
              <span class="opacity-70">Year:</span><br>
              <span class="font-medium">2024</span>
            </div>
            <div>
              <span class="opacity-70">Role:</span><br>
              <span class="font-medium">UI/UX Design, Front-end Development</span>
            </div>
          </div>
        </div>

        <div class="animate delay-[200ms]">
          <h4 class="font-semibold mb-4">Technologies Used</h4>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span>CSS</span>
              <span class="opacity-70">42.3%</span>
            </div>
            <div class="flex justify-between">
              <span>HTML</span>
              <span class="opacity-70">31.3%</span>
            </div>
            <div class="flex justify-between">
              <span>JavaScript</span>
              <span class="opacity-70">26.4%</span>
            </div>
          </div>
        </div>

        <div class="animate delay-[300ms]">
          <h4 class="font-semibold mb-4">Results</h4>
          <div class="space-y-3 text-sm">
            <div class="flex justify-between">
              <span>Performance:</span>
              <span class="font-medium text-green-400">80</span>
            </div>
            <div class="flex justify-between">
              <span>Accessibility:</span>
              <span class="font-medium text-green-400">82</span>
            </div>
            <div class="flex justify-between">
              <span>Best Practices:</span>
              <span class="font-medium text-green-400">100</span>
            </div>
            <div class="flex justify-between">
              <span>SEO:</span>
              <span class="font-medium text-green-400">91</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <div class="divider mx-6"></div>

  <!-- NEXT PROJECT -->
  <section class="mx-auto max-w-7xl px-6 py-24">
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-2xl font-semibold tracking-tight animate">Next Project</h3>
        <p class="mt-2 text-sm opacity-70 animate delay-[100ms]">Pulse SaaS Dashboard</p>
      </div>
      <a href="pulse-details.html" class="flex items-center gap-2 text-sm font-medium px-4 py-2 outline rounded-md hover:bg-white/5 animate delay-[200ms]">
        View case study <i data-lucide="arrow-right" class="stroke-[1.5] w-4 h-4"></i>
      </a>
    </div>
  </section>

  <footer class="mt-auto py-10 px-6 text-center text-[11px] opacity-60">
    © <span id="year"></span> Jaykee Aba-a. Built & designed by me.
  </footer>

  <script>
    // Lucide
    lucide.createIcons({attrs:{'stroke-width':1.5}});

    // Mobile nav toggle
    const menuBtn=document.getElementById('menuBtn'), mobileNav=document.getElementById('mobileNav');
    menuBtn.addEventListener('click',()=>mobileNav.classList.toggle('hidden'));

    // Scroll animations
    const observer=new IntersectionObserver(entries=>{
      entries.forEach(e=>{if(e.isIntersecting){e.target.classList.add('show');observer.unobserve(e.target);}})
    },{threshold:.12});
    document.querySelectorAll('.animate').forEach(el=>observer.observe(el));

    // year
    document.getElementById('year').textContent=new Date().getFullYear();
  </script>
</body>
</html>
