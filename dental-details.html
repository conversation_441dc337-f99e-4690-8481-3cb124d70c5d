<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Dental Smyle - Case Study | Jaykee Aba-a</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://fonts.bunny.net/css?family=inter:300,400,500,600,700|space-grotesk:500" rel="stylesheet">
  <script src="https://unpkg.com/lucide@latest"></script>
  <style>
    body{font-family:'Inter',sans-serif;background:#0e0e10;color:#e5e7eb;}
    .animate{opacity:0;transform:translateY(40px);transition:all .8s cubic-bezier(.22,1,.36,1);}
    .animate.show{opacity:1;transform:none;}
    .divider{border-top:1px solid rgba(255,255,255,.08);}
    .outline{outline:1px solid rgba(255,255,255,.08);}
    ::selection{background:#6366f1;color:#fff;}
    a{transition:.3s;}
    a:hover{color:#a5b4fc;}
    .gradient-text{background:linear-gradient(90deg,#6366f1,#8b5cf6 60%,#ec4899);-webkit-background-clip:text;color:transparent;}
    .dental-blue{color:#0ea5e9;}
    .dental-bg{background:linear-gradient(135deg,#0ea5e9,#0284c7);}
  </style>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="min-h-screen flex flex-col">
  <!-- NAV -->
  <header class="fixed top-0 inset-x-0 z-50 backdrop-blur-sm bg-black/30">
    <div class="mx-auto max-w-7xl flex items-center justify-between px-6 py-4">
      <a href="v2.html" class="text-lg tracking-tight font-semibold gradient-text">JK</a>
      <nav class="hidden sm:flex items-center gap-6 text-sm">
        <a href="v2.html#work">Work</a>
        <a href="v2.html#services">Services</a>
        <a href="v2.html#about">About</a>
        <a href="v2.html#contact">Contact</a>
      </nav>
      <div class="sm:flex hidden items-center gap-4">
        <a href="v2.html#contact" class="text-xs font-medium px-4 py-2 outline rounded-md hover:bg-white/5">Let's talk</a>
      </div>
    </div>
  </header>

  <!-- HERO -->
  <section class="relative px-6 pt-32 pb-12">
    <div class="mx-auto max-w-6xl">
      <a href="v2.html#work" class="inline-flex items-center gap-2 text-sm opacity-70 hover:opacity-100 mb-8 animate">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        Back to portfolio
      </a>
      <h1 class="text-4xl sm:text-5xl font-semibold tracking-tight animate delay-[100ms]">
        Dental Smyle
      </h1>
      <p class="mt-4 text-lg opacity-80 animate delay-[200ms]">
        Professional dental practice website featuring modern design and comprehensive dental services.
      </p>
      <div class="mt-8 flex gap-4 animate delay-[300ms]">
        <a href="https://dental-smyle.vercel.app/" target="_blank" class="px-6 py-3 bg-white text-black rounded-md text-sm font-medium hover:bg-white/90">
          Visit website
        </a>
        <button class="px-6 py-3 outline rounded-md text-sm font-medium hover:bg-white/5 flex items-center gap-2">
          View details <i data-lucide="chevron-down" class="w-4 h-4"></i>
        </button>
      </div>
    </div>
  </section>

  <!-- MAIN IMAGE -->
  <section class="mx-auto max-w-6xl px-6 py-8">
    <div class="animate">
      <img src="dental.png" alt="Dental Smyle Website" class="w-full rounded-lg outline">
    </div>
  </section>

  <!-- PROJECT OVERVIEW -->
  <section class="mx-auto max-w-6xl px-6 py-16">
    <div class="grid lg:grid-cols-3 gap-12">
      <!-- Left Column - Project Overview -->
      <div class="lg:col-span-2">
        <h2 class="text-2xl font-semibold tracking-tight mb-6 animate">Project Overview</h2>
        <p class="text-sm opacity-80 leading-relaxed mb-6 animate delay-[100ms]">
          I'll build a modern dental practice website for Bright Smile Dental with a professional blue color palette. This evokes trust, cleanliness, and medical expertise focused on patient care and comprehensive dental services.
        </p>
        <p class="text-sm opacity-80 leading-relaxed mb-8 animate delay-[200ms]">
          <strong>Design Vision:</strong> Clean, professional-inspired blue palette with white and light tones. Clean, modern interface emphasizing dental expertise and patient comfort. Local practice focus and comprehensive service transparency. Mobile-first responsive design.
        </p>

        <h3 class="text-xl font-semibold mb-4 animate delay-[300ms]">Key Features</h3>
        <ul class="space-y-3 text-sm animate delay-[400ms]">
          <li class="flex items-start gap-2">
            <i data-lucide="check" class="w-4 h-4 dental-blue mt-0.5 flex-shrink-0"></i>
            <span><strong>Service catalog</strong> with comprehensive dental services, treatment details</span>
          </li>
          <li class="flex items-start gap-2">
            <i data-lucide="check" class="w-4 h-4 dental-blue mt-0.5 flex-shrink-0"></i>
            <span><strong>Smart appointment booking</strong> with contact form</span>
          </li>
          <li class="flex items-start gap-2">
            <i data-lucide="check" class="w-4 h-4 dental-blue mt-0.5 flex-shrink-0"></i>
            <span><strong>Patient testimonials</strong> and success stories</span>
          </li>
          <li class="flex items-start gap-2">
            <i data-lucide="check" class="w-4 h-4 dental-blue mt-0.5 flex-shrink-0"></i>
            <span><strong>Professional team showcase</strong> and practice information</span>
          </li>
          <li class="flex items-start gap-2">
            <i data-lucide="check" class="w-4 h-4 dental-blue mt-0.5 flex-shrink-0"></i>
            <span><strong>Emergency care information</strong> and contact details</span>
          </li>
        </ul>

        <h3 class="text-xl font-semibold mb-4 mt-8 animate delay-[500ms]">Color Palette & Style</h3>
        <p class="text-sm mb-4 animate delay-[600ms]">
          <strong>Primary:</strong> Professional dental blue
        </p>
        <p class="text-sm opacity-80 animate delay-[700ms]">
          <strong>Accents:</strong> Clean whites, light grays <strong>Typography:</strong> Clean, readable fonts <strong>Animation:</strong> Smooth, professional transitions
        </p>
      </div>

      <!-- Right Column - Project Details -->
      <div class="space-y-8">
        <div class="animate delay-[100ms]">
          <h3 class="text-lg font-semibold mb-4">Project Details</h3>
          <div class="space-y-4 text-sm">
            <div>
              <p class="opacity-70 mb-1">Client:</p>
              <p class="font-medium">Bright Smile Dental</p>
            </div>
            <div>
              <p class="opacity-70 mb-1">Year:</p>
              <p class="font-medium">2025</p>
            </div>
            <div>
              <p class="opacity-70 mb-1">Role:</p>
              <p class="font-medium">UI/UX Design, Front-end Development</p>
            </div>
          </div>
        </div>

        <div class="animate delay-[200ms]">
          <h3 class="text-lg font-semibold mb-4">Technologies Used</h3>
          <div class="space-y-3">
            <div class="flex items-center gap-3">
              <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span class="text-sm">CSS</span>
              <span class="text-xs opacity-70">(42.3%)</span>
            </div>
            <div class="flex items-center gap-3">
              <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
              <span class="text-sm">HTML</span>
              <span class="text-xs opacity-70">(31.3%)</span>
            </div>
            <div class="flex items-center gap-3">
              <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <span class="text-sm">JavaScript</span>
              <span class="text-xs opacity-70">(26.4%)</span>
            </div>
          </div>
        </div>

        <div class="animate delay-[300ms]">
          <h3 class="text-lg font-semibold mb-4">Results</h3>
          <div class="space-y-3 text-sm">
            <div class="flex justify-between">
              <span class="opacity-70">Performance:</span>
              <span class="font-medium text-green-400">80</span>
            </div>
            <div class="flex justify-between">
              <span class="opacity-70">Accessibility:</span>
              <span class="font-medium text-green-400">82</span>
            </div>
            <div class="flex justify-between">
              <span class="opacity-70">Best Practices:</span>
              <span class="font-medium text-green-400">100</span>
            </div>
            <div class="flex justify-between">
              <span class="opacity-70">SEO:</span>
              <span class="font-medium text-green-400">91</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <div class="divider mx-6"></div>

  <!-- NEXT PROJECT -->
  <section class="mx-auto max-w-6xl px-6 py-16">
    <div class="flex justify-between items-center">
      <div class="animate">
        <h2 class="text-2xl font-semibold tracking-tight mb-2">Next Project</h2>
        <p class="text-sm opacity-70">Bella Filipina Portfolio</p>
      </div>
      <a href="aurora-details.html" class="animate delay-[100ms] inline-flex items-center gap-2 text-sm font-medium px-4 py-2 outline rounded-md hover:bg-white/5">
        View case study <i data-lucide="arrow-right" class="w-4 h-4"></i>
      </a>
    </div>
  </section>

  <footer class="mt-auto py-10 px-6 text-center text-[11px] opacity-60">
    © <span id="year"></span> Jaykee Aba-a. Built & designed by me.
  </footer>

  <script>
    // Lucide icons
    lucide.createIcons({attrs:{'stroke-width':1.5}});

    // Scroll animations
    const observer = new IntersectionObserver(entries => {
      entries.forEach(e => {
        if (e.isIntersecting) {
          e.target.classList.add('show');
          observer.unobserve(e.target);
        }
      })
    }, {threshold: .12});
    document.querySelectorAll('.animate').forEach(el => observer.observe(el));

    // Year
    document.getElementById('year').textContent = new Date().getFullYear();
  </script>
</body>
</html>
